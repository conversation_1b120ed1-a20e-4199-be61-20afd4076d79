// src/pages/Reports/MarketersFeedbackReport.tsx
import React from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { MessageSquare, CalendarDays, User, Users } from "lucide-react";

const MarketersFeedbackReport: React.FC<{ data: any }> = ({ data }) => {
  if (!data || !data.results?.length) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Feedback Data</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No marketers' feedback data found for the selected date range.
          </p>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "-";
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getInitials = (name: string) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-blue-600" />
          Marketers' Feedback Report
        </CardTitle>
        <CardDescription>
          Site visits client feedback for marketers (shows outcome, remarks, and project info)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead>
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4" />
                    Date
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Marketer
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Client(s)
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-2">
                    Project
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-2">
                    Outcome
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center gap-2">
                    Remarks
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.results.map((item: any, i: number) => (
                <TableRow key={item.site_visit_id || i} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <CalendarDays className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{formatDate(item.pickup_date)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-primary/10 text-primary text-xs">
                          {getInitials(item.marketer_name)}
                        </AvatarFallback>
                      </Avatar>
                      <span>{item.marketer_name || "-"}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {item.clients || "-"}
                  </TableCell>
                  <TableCell>
                    {item.project_name || "-"}
                  </TableCell>
                  <TableCell>
                    {item.outcome_summary || "-"}
                  </TableCell>
                  <TableCell>
                    {item.visit_remarks || "-"}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
export default MarketersFeedbackReport;
