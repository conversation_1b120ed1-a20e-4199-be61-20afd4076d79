import React, { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, Send, } from 'lucide-react';
import { Outlet } from 'react-router-dom';

interface Message {
    id: string;
    text: string;
    sender: 'user' | 'contact';
    timestamp: Date;
}

interface ChatComponentProps {
    contactName?: string;
    contactAvatar?: string;
    onlineStatus?: boolean;
}

const ChatComponent: React.FC<ChatComponentProps> = ({
    contactName = "AI Support",
    contactAvatar = "https://via.placeholder.com/40/22c55e/ffffff?text=S",
    onlineStatus = true
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [messages, setMessages] = useState<Message[]>([
        {
            id: '1',
            text: 'Hello! How can I help you today?',
            sender: 'contact',
            timestamp: new Date(Date.now() - 60000)
        }
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleSendMessage = () => {
        if (inputMessage.trim()) {
            const newMessage: Message = {
                id: Date.now().toString(),
                text: inputMessage,
                sender: 'user',
                timestamp: new Date()
            };
            setMessages([...messages, newMessage]);
            setInputMessage('');

            // Simulate response after 1.5 seconds
            setTimeout(() => {
                const response: Message = {
                    id: (Date.now() + 1).toString(),
                    text: 'Thanks for your message! I\'ll get back to you shortly.',
                    sender: 'contact',
                    timestamp: new Date()
                };
                setMessages(prev => [...prev, response]);
            }, 1500);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    const formatTime = (date: Date) => {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    return (
        <>
            <Outlet />
            {/* Floating Chat Button */}
            <button
                onClick={() => setIsOpen(true)}
                className={`
                    fixed bottom-6 right-6 z-50
                    w-14 h-14 bg-primary hover:bg-emerald-600 
                    rounded-full shadow-lg hover:shadow-xl
                    flex items-center justify-center
                    transition-all duration-300 transform hover:scale-110
                    ${isOpen ? 'opacity-0 pointer-events-none' : 'opacity-100'}
                `}
            >
                <MessageCircle className="w-6 h-6 text-white" />
            </button>

            {/* Chat Modal */}
            {isOpen && (
                <div className="fixed bottom-6 right-6 z-50 w-80 h-[25rem] bg-white dark:bg-gray-800 rounded-lg shadow-2xl overflow-hidden">
                    {/* Header */}
                    <div className="bg-primary text-white p-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className='space-y-0.5'>
                                <h3 className="font-semibold text-sm">{contactName}</h3>
                                <div className='flex gap-1 items-center'>
                                    <span className="relative flex size-3">
                                        <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-emerald-200 opacity-75"></span>
                                        <span className="relative inline-flex size-3 rounded-full bg-emerald-400"></span>
                                    </span>
                                    <p className="text-xs opacity-90">{onlineStatus ? 'Online' : 'Last seen recently'}</p>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => setIsOpen(false)}
                                className="p-1 hover:bg-emerald-600 rounded-full transition-colors"
                            >
                                <X className="w-6 h-6" />
                            </button>
                        </div>
                    </div>

                    {/* Messages Area */}
                    <div className="flex-1 overflow-y-auto p-4 space-y-3 h-64 bg-gray-50 dark:bg-gray-700">
                        {messages.map((message) => (
                            <div
                                key={message.id}
                                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                            >
                                <div
                                    className={`max-w-xs px-3 py-2 rounded-lg text-sm
                                        ${message.sender === 'user'
                                            ? 'bg-primary text-white rounded-br-none'
                                            : 'bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 rounded-bl-none shadow-sm'
                                        }
                                    `}
                                >
                                    <p>{message.text}</p>
                                    <p className={`text-xs mt-1 ${message.sender === 'user' ? 'text-emerald-100' : 'text-gray-500 dark:text-gray-400'}`}>
                                        {formatTime(message.timestamp)}
                                    </p>
                                </div>
                            </div>
                        ))}
                        <div ref={messagesEndRef} />
                    </div>

                    {/* Input Area */}
                    <div className="p-4 bg-white dark:bg-gray-800 border-t dark:border-gray-600">
                        <div className="flex items-center space-x-2">
                            <input
                                type="text"
                                value={inputMessage}
                                onChange={(e) => setInputMessage(e.target.value)}
                                onKeyPress={handleKeyPress}
                                placeholder="Type a message..."
                                className="
                                    flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 
                                    rounded-full focus:outline-none focus:ring-2 focus:ring-primary
                                    dark:bg-gray-700 dark:text-gray-100
                                    "
                            />
                            <button
                                onClick={handleSendMessage}
                                disabled={!inputMessage.trim()}
                                className={`
                                        p-3 rounded-full transition-colors
                                        ${inputMessage.trim()
                                        ? 'bg-primary text-white  transition-all duration-300 transform hover:scale-110'
                                        : 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                                    }
                                `}
                            >
                                <Send className="w-5 h-5 -translate-x-[1px] translate-y-[1px]" />
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ChatComponent;