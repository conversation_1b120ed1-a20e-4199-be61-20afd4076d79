// src/pages/Reports/MostBookedSitesReport.tsx
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { MapPin, Building2, TrendingUp, Trophy, Star, Calendar } from "lucide-react";

const MostBookedSitesReport: React.FC<{ data: any }> = ({ data }) => {
  if (!data || !data.results?.length) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Site Data</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No site booking data found for the selected date range.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Sort sites by bookings to show most popular first
  const sortedSites = [...data.results].sort((a, b) => (b.bookings || 0) - (a.bookings || 0));
  
  // Get top 3 sites for highlighting
  const topSites = sortedSites.slice(0, 3);
  const maxBookings = Math.max(...sortedSites.map(site => site.bookings || 0));

  const getRankBadge = (index: number) => {
    switch (index) {
      case 0:
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"><Trophy className="w-3 h-3 mr-1" />1st</Badge>;
      case 1:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200"><Star className="w-3 h-3 mr-1" />2nd</Badge>;
      case 2:
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200"><Star className="w-3 h-3 mr-1" />3rd</Badge>;
      default:
        return null;
    }
  };

  const getBookingsBadge = (bookings: number) => {
    if (bookings >= maxBookings * 0.8) {
      return <Badge variant="default" className="bg-green-100 text-green-800">{bookings}</Badge>;
    } else if (bookings >= maxBookings * 0.5) {
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">{bookings}</Badge>;
    } else {
      return <Badge variant="outline">{bookings}</Badge>;
    }
  };

  // Calculate total bookings
  const totalBookings = sortedSites.reduce((sum, site) => sum + (site.bookings || 0), 0);

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Most Booked Sites Report
        </CardTitle>
        <CardDescription>
          Analysis of site popularity and booking performance across projects
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Sites</p>
                <p className="text-2xl font-bold text-blue-600">{sortedSites.length}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Bookings</p>
                <p className="text-2xl font-bold text-green-600">{totalBookings}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Avg. Bookings</p>
                <p className="text-2xl font-bold text-purple-600">
                  {totalBookings > 0 ? Math.round(totalBookings / sortedSites.length) : 0}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Top 3 Sites Highlight */}
        {topSites.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-600" />
              Top Performing Sites
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {topSites.map((site, index) => (
                <Card key={site.id || index} className="p-4 border-2 border-primary/20">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      {getRankBadge(index)}
                      <span className="text-2xl font-bold text-primary">{site.bookings || 0}</span>
                    </div>
                    <h4 className="font-semibold truncate">{site.site_name || 'Unknown Site'}</h4>
                    <p className="text-sm text-muted-foreground truncate">{site.project_name || 'Unknown Project'}</p>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Booking Rate</span>
                        <span className="font-medium">{site.booking_rate || 0}%</span>
                      </div>
                      <Progress value={site.booking_rate || 0} className="h-2" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Detailed Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">Rank</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Site Name
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Project
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-center">
                  <div className="flex items-center gap-2 justify-center">
                    <Calendar className="h-4 w-4" />
                    Bookings
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Booking Rate
                  </div>
                </TableHead>
                <TableHead className="font-semibold">Popularity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedSites.map((site: any, i: number) => (
                <TableRow key={site.id || i} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-lg text-muted-foreground">#{i + 1}</span>
                      {i < 3 && getRankBadge(i)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <MapPin className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">{site.site_name || "-"}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      {site.project_name || "-"}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    {getBookingsBadge(site.bookings ?? 0)}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{site.booking_rate ?? 0}%</span>
                      </div>
                      <Progress 
                        value={site.booking_rate ?? 0} 
                        className="h-2"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Progress 
                        value={maxBookings > 0 ? ((site.bookings || 0) / maxBookings) * 100 : 0} 
                        className="h-2"
                      />
                      <span className="text-xs text-muted-foreground">
                        {maxBookings > 0 ? Math.round(((site.bookings || 0) / maxBookings) * 100) : 0}% of max
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="text-sm text-muted-foreground">
          Showing {sortedSites.length} site{sortedSites.length !== 1 ? 's' : ''} ranked by booking frequency
        </div>
      </CardContent>
    </Card>
  );
};
export default MostBookedSitesReport;
