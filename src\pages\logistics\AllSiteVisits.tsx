
import { useState, useMemo } from 'react';
import { Screen } from "@/app-components/layout/screen";
import {
  Eye,
  Edit,
  Trash2,
  Plus,
  Download,
  MapPin,
  Calendar,
  Clock,
  User,
  Car,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  useGetSiteVisitsQuery,
  useDeleteSiteVisitMutation,
  SiteVisit
} from "@/redux/slices/logistics";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import { format } from 'date-fns';

const AllSiteVisits = () => {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [selectedVisit, setSelectedVisit] = useState<SiteVisit | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // API hooks
  const {
    data: siteVisitsResponse,
    isLoading,
    isError,
    refetch
  } = useGetSiteVisitsQuery({
    page: currentPage,
    page_size: pageSize,
    search: searchValue,
  });

  const [deleteSiteVisit, { isLoading: isDeleting }] = useDeleteSiteVisitMutation();

  // Transform API data
  const siteVisits = useMemo(() => {
    return siteVisitsResponse?.data?.results || [];
  }, [siteVisitsResponse]);

  // Filter data based on active tab
  const filteredData = useMemo(() => {
    if (activeTab === 'all') return siteVisits;
    return siteVisits.filter((visit: SiteVisit) =>
      visit.status.toLowerCase() === activeTab.toLowerCase()
    );
  }, [siteVisits, activeTab]);

  // Action handlers
  const handleAction = (action: string, visit: SiteVisit) => {
    switch (action) {
      case 'view':
        navigate(`/logistics/site-visits/${visit.id}`);
        break;
      case 'edit':
        navigate(`/logistics/site-visits/${visit.id}`);
        break;
      case 'delete':
        setSelectedVisit(visit);
        setDeleteModalOpen(true);
        break;
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedVisit?.id) return;

    try {
      await deleteSiteVisit(selectedVisit.id).unwrap();
      toast.success('Site visit deleted successfully');
      setDeleteModalOpen(false);
      setSelectedVisit(null);
      refetch();
    } catch (error) {
      toast.error('Failed to delete site visit');
    }
  };

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'completed':
        return 'secondary';
      case 'pending':
      case 'in progress':
        return 'outline';
      case 'rejected':
      case 'cancelled':
        return 'destructive';
      default:
        return 'default';
    }
  };

  // Statistics
  const stats = useMemo(() => {
    const total = siteVisits.length;
    const pending = siteVisits.filter((v: SiteVisit) => v.status === 'Pending').length;
    const approved = siteVisits.filter((v: SiteVisit) => v.status === 'Approved').length;
    const completed = siteVisits.filter((v: SiteVisit) => v.status === 'Completed').length;
    const rejected = siteVisits.filter((v: SiteVisit) => v.status === 'Rejected').length;

    return { total, pending, approved, completed, rejected };
  }, [siteVisits]);

  // Enhanced columns definition
  const columns: ColumnDef<SiteVisit>[] = useMemo(() => [
    {
      accessorKey: "id",
      header: "ID",
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          #{row.original.id}
        </div>
      ),
      enableColumnFilter: true,
    },
    {
      id: "dateTime",
      header: "Schedule",
      cell: ({ row }) => {
        const visit = row.original;
        return (
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <div className="font-medium text-sm">
                {format(new Date(visit.pickup_date), 'MMM dd, yyyy')}
              </div>
              <div className="text-xs text-gray-500 flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {visit.pickup_time}
              </div>
            </div>
          </div>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "project",
      header: "Project",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center">
            <MapPin className="w-4 h-4 text-green-600" />
          </div>
          <div>
            <div className="font-medium text-sm">{row.original.project}</div>
            <div className="text-xs text-gray-500">Site Visit</div>
          </div>
        </div>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-purple-50 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-purple-600" />
          </div>
          <div className="font-medium text-sm">{row.original.marketer}</div>
        </div>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "pickup_location",
      header: "Pickup Location",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{row.original.pickup_location}</span>
        </div>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "driver",
      header: "Driver",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          {row.original.driver ? (
            <>
              <div className="w-8 h-8 bg-orange-50 rounded-full flex items-center justify-center">
                <Car className="w-4 h-4 text-orange-600" />
              </div>
              <span className="text-sm font-medium">{row.original.driver}</span>
            </>
          ) : (
            <span className="text-xs text-gray-400 italic">Not assigned</span>
          )}
        </div>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge variant={getStatusVariant(status)} className="text-xs">
            {status}
          </Badge>
        );
      },
      enableColumnFilter: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const visit = row.original;
        return (
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction('view', visit)}
              className="h-8 w-8 p-0 hover:bg-blue-50"
            >
              <Eye className="h-4 w-4 text-blue-600" />
              <span className="sr-only">View</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction('edit', visit)}
              className="h-8 w-8 p-0 hover:bg-green-50"
            >
              <Edit className="h-4 w-4 text-green-600" />
              <span className="sr-only">Edit</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction('delete', visit)}
              className="h-8 w-8 p-0 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 text-red-600" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    }
  ], []);

  // Search component
  const SearchComponent = () => (
    <div className="relative max-w-md">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gray-400" />
      </div>
      <Input
        type="text"
        placeholder="Search site visits..."
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="pl-10 pr-4 py-2 border-gray-200 focus:ring-2 focus:ring-primary focus:border-primary"
      />
    </div>
  );

  return (
    <Screen>
      <div className="space-y-8 py-8">
        {/* Enhanced Header Section */}
        <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardHeader className="pb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <MapPin className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Site Visits Management</CardTitle>
                  <CardDescription className="text-gray-600 mt-1">
                    Track and manage all site visit schedules and assignments
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetch()}
                  disabled={isLoading}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                  <span>Refresh</span>
                </Button>
                <PrimaryButton
                  variant="primary"
                  className="flex items-center space-x-2 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all"
                >
                  <Plus className="w-4 h-4" />
                  <span>Schedule Visit</span>
                </PrimaryButton>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Visits</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
                </div>
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-5 h-5 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
                </div>
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.completed}</p>
                </div>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
                </div>
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <Trash2 className="w-5 h-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced DataTable Section */}
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <Tabs defaultValue="all" className="space-y-6" onValueChange={setActiveTab}>
              <div className="flex items-center justify-between">
                <TabsList className="grid w-full max-w-md grid-cols-4">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                  <TabsTrigger value="approved">Approved</TabsTrigger>
                  <TabsTrigger value="completed">Completed</TabsTrigger>
                </TabsList>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="flex items-center space-x-2">
                    <Filter className="w-4 h-4" />
                    <span>Filter</span>
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>Export</span>
                  </Button>
                </div>
              </div>

              <TabsContent value={activeTab} className="mt-0">
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-500">Loading site visits...</span>
                  </div>
                ) : isError ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Trash2 className="w-8 h-8 text-red-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading data</h3>
                    <p className="text-gray-500 mb-6">There was an error loading the site visits.</p>
                    <Button onClick={() => refetch()} className="flex items-center space-x-2">
                      <RefreshCw className="w-4 h-4" />
                      <span>Try Again</span>
                    </Button>
                  </div>
                ) : (
                  <DataTable<SiteVisit>
                    data={filteredData}
                    columns={columns}
                    title="Site Visits Directory"
                    enableToolbar={true}
                    enableExportToExcel={true}
                    enablePrintPdf={true}
                    enablePagination={true}
                    enableColumnFilters={true}
                    enableSorting={true}
                    enableSelectColumn={false}
                    searchInput={<SearchComponent />}
                    containerClassName="border-0"
                    tableClassName="border-0"
                    tHeadClassName="bg-gray-50/50"
                    tHeadCellsClassName="border-b border-gray-200 px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider"
                    tBodyCellsClassName="px-6 py-4 whitespace-nowrap border-b border-gray-100"
                    tBodyTrClassName="hover:bg-gray-50/50 transition-colors"
                  />
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Delete Confirmation Modal */}
        <ConfirmModal
          isOpen={deleteModalOpen}
          onOpenChange={setDeleteModalOpen}
          variant="danger"
          title="Delete Site Visit"
          message={
            <p>
              Are you sure you want to delete the site visit for <strong>{selectedVisit?.project}</strong>?
              This action cannot be undone.
            </p>
          }
          confirmText={isDeleting ? "Deleting..." : "Yes, Delete"}
          onConfirm={handleDeleteConfirm}
          disabled={isDeleting}
        />
      </div>
    </Screen>
  );
};

export default AllSiteVisits;