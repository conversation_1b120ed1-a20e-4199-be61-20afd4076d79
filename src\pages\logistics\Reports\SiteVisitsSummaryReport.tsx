// src/pages/Reports/SiteVisitsSummaryReport.tsx
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { BarChart3, User, Building2, TrendingUp, CheckCircle, Clock, XCircle, MapPin, Route } from "lucide-react";

const SiteVisitsSummaryReport: React.FC<{ data: any }> = ({ data }) => {
  if (!data || !data.results?.length) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Summary Data</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No site visits summary data found for the selected date range.
          </p>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (count: number, type: string) => {
    if (count === 0) return <span className="text-muted-foreground">0</span>;
    
    switch (type) {
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800">{count}</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">{count}</Badge>;
      case 'enroute':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">{count}</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">{count}</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">{count}</Badge>;
      case 'rejected':
        return <Badge variant="destructive">{count}</Badge>;
      default:
        return <span className="font-medium">{count}</span>;
    }
  };

  const getProgressColor = (rate: number) => {
    if (rate >= 80) return "bg-green-500";
    if (rate >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Calculate totals
  const totals = data.results.reduce((acc: any, row: any) => {
    acc.total_visits += row.total_site_visits || 0;
    acc.approved += row.approved_visits || 0;
    acc.completed += row.completed_visits || 0;
    acc.enroute += row.enroute_visits || 0;
    acc.pending += row.pending_visits || 0;
    acc.cancelled += row.cancelled_visits || 0;
    acc.rejected += row.rejected_visits || 0;
    return acc;
  }, {
    total_visits: 0,
    approved: 0,
    completed: 0,
    enroute: 0,
    pending: 0,
    cancelled: 0,
    rejected: 0
  });

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-blue-600" />
          Site Visits Summary Report
        </CardTitle>
        <CardDescription>
          Comprehensive overview of site visits performance by marketer and project
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold text-blue-600">{totals.total_visits}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Approved</p>
                <p className="text-2xl font-bold text-green-600">{totals.approved}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-blue-600">{totals.completed}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <Route className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Enroute</p>
                <p className="text-2xl font-bold text-purple-600">{totals.enroute}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{totals.pending}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-gray-600" />
              <div>
                <p className="text-sm text-muted-foreground">Cancelled</p>
                <p className="text-2xl font-bold text-gray-600">{totals.cancelled}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Rejected</p>
                <p className="text-2xl font-bold text-red-600">{totals.rejected}</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Detailed Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Marketer
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Project
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-center">Total</TableHead>
                <TableHead className="font-semibold text-center">Approved</TableHead>
                <TableHead className="font-semibold text-center">Completed</TableHead>
                <TableHead className="font-semibold text-center">Enroute</TableHead>
                <TableHead className="font-semibold text-center">Pending</TableHead>
                <TableHead className="font-semibold text-center">Cancelled</TableHead>
                <TableHead className="font-semibold text-center">Rejected</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Approval Rate
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Completion Rate
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.results.map((row: any, i: number) => (
                <TableRow key={row.marketer_id || i} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">{row.marketer_name || "-"}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      {row.project_name || "-"}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="font-semibold text-lg">{row.total_site_visits ?? "-"}</span>
                  </TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(row.approved_visits ?? 0, 'approved')}
                  </TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(row.completed_visits ?? 0, 'completed')}
                  </TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(row.enroute_visits ?? 0, 'enroute')}
                  </TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(row.pending_visits ?? 0, 'pending')}
                  </TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(row.cancelled_visits ?? 0, 'cancelled')}
                  </TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(row.rejected_visits ?? 0, 'rejected')}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{row.approval_rate ?? 0}%</span>
                      </div>
                      <Progress 
                        value={row.approval_rate ?? 0} 
                        className="h-2"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{row.completion_rate ?? 0}%</span>
                      </div>
                      <Progress 
                        value={row.completion_rate ?? 0} 
                        className="h-2"
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="text-sm text-muted-foreground">
          Showing {data.results.length} marketer{data.results.length !== 1 ? 's' : ''} with site visit data
        </div>
      </CardContent>
    </Card>
  );
};
export default SiteVisitsSummaryReport;
