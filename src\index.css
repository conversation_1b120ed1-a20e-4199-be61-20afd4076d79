@tailwind base;
@tailwind components;
@tailwind utilities;

/* -----------------------------------
  Light Theme 
----------------------------------- */
:root {
  color-scheme: light;

  font-family: "Manrope", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
  font-optical-sizing: auto;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Light theme tokens */
  --background: 104 5% 96%;
  --foreground: 240 18% 38%;
  --card: 232 25% 92%;
  --card-foreground: 240 18% 38%;
  --popover: 0 0% 98%;
  --popover-foreground: 240 18% 38%;
  --primary: 170 58% 28%;
  --primary-foreground: 104 5% 96%;
  --secondary: 103 42% 92%;
  --secondary-foreground: 240 18% 38%;
  --muted: 232 10% 84%;
  --muted-foreground: 240 18% 38%;
  --accent: 208 47% 85%;
  --accent-foreground: 104 5% 96%;

  --success: 144.9 80.4% 40%;
  --success-foreground: 0 0% 100%;

  --destructive: 1 63% 51%;
  --destructive-foreground: 0 0% 100%;
  --border: 232 10% 84%;
  --input: 0 0% 98%;
  --ring: 240 10% 61%;
  --chart-1: 240 18% 38%;
  --chart-2: 232 10% 84%;
  --chart-3: 240 10% 61%;
  --chart-4: 220 70% 50%;
  --chart-5: 160 60% 45%;
  --radius: 0.5rem;

  /* Sidebar tokens for Light */
  --sidebar-background: 103 42% 92%;
  --sidebar-foreground: 170 58% 28%;
  --sidebar-primary: 170 58% 28%;
  --sidebar-primary-foreground: 103 42% 92%;
  --sidebar-accent: 170 58% 38%;
  --sidebar-accent-foreground: 103 42% 92%;
  --sidebar-border: 170 58% 28%;
  --sidebar-ring: 170 58% 28%;
}

/* -----------------------------------
   Dark Theme 
----------------------------------- */
.dark {
  color-scheme: dark;

  --background: 0 0% 12%;
  --foreground: 0 0% 98%;
  --card: 0 0% 15%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 12%;
  --popover-foreground: 0 0% 98%;
  --primary: 210 100% 40%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 25%;
  --secondary-foreground: 0 0% 100%;
  --muted: 0 0% 18%;
  --muted-foreground: 0 0% 80%;
  --accent: 210 100% 40%;
  --accent-foreground: 0 0% 100%;

  /*
    UPDATED success color in dark theme:
    Increase lightness so it’s clearly visible against a dark background
  */
  --success: 142.4 71.8% 45%;
  --success-foreground: 0 0% 100%;

  --destructive: 0 62% 30%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 18%;
  --input: 0 0% 18%;
  --ring: 210 100% 40%;
  --chart-1: 210 100% 40%;
  --chart-2: 0 0% 25%;
  --chart-3: 0 0% 18%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --radius: 0.5rem;

  /* Sidebar tokens for Dark */
  --sidebar-background: 170 58% 15%;
  --sidebar-foreground: 103 42% 92%;
  --sidebar-primary: 170 58% 28%;
  --sidebar-primary-foreground: 103 42% 92%;
  --sidebar-accent: 170 58% 22%;
  --sidebar-accent-foreground: 103 42% 92%;
  --sidebar-border: 170 58% 22%;
  --sidebar-ring: 170 58% 28%;
}

body {
  margin: 0;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
  }
}

/* Custom scrollbar styles for better mobile experience */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: #d1d5db transparent;
  }

  .scrollbar-track-gray-100 {
    scrollbar-color: #d1d5db #f3f4f6;
  }

  /* Webkit scrollbar styles for better cross-browser support */
  .scrollbar-thin::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Dark theme scrollbar */
  .dark .scrollbar-thin::-webkit-scrollbar-track {
    background: #374151;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #6b7280;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Enhanced mobile touch scrolling */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Table specific scrolling optimizations for all screen sizes */
  .table-mobile-scroll {
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
    width: 100%;
    max-width: 100%;
  }

  .table-mobile-scroll::-webkit-scrollbar {
    height: 12px;
  }

  .table-mobile-scroll::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 6px;
  }

  .table-mobile-scroll::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 6px;
    border: 2px solid #f3f4f6;
  }

  .table-mobile-scroll::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Dark theme for table scroll */
  .dark .table-mobile-scroll {
    scrollbar-color: #6b7280 #374151;
  }

  .dark .table-mobile-scroll::-webkit-scrollbar-track {
    background: #374151;
  }

  .dark .table-mobile-scroll::-webkit-scrollbar-thumb {
    background: #6b7280;
    border: 2px solid #374151;
  }

  .dark .table-mobile-scroll::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Ensure tables don't overflow on larger screens */
  @media (min-width: 768px) {
    .table-mobile-scroll {
      max-width: 100%;
      overflow-x: auto;
    }

    .table-mobile-scroll table {
      width: 100%;
      table-layout: auto;
    }
  }

  @media (min-width: 1024px) {
    .table-mobile-scroll {
      max-width: calc(100vw - 2rem);
    }
  }

  @media (min-width: 1280px) {
    .table-mobile-scroll {
      max-width: calc(100vw - 4rem);
    }
  }
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
:root {
  --animate-wigglee: rotate 1s ease-in-out infinite;
}

@keyframes rotate {
  0%,
  100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}